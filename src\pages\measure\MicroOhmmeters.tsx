import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowRight, 
  Check, 
  Zap, 
  Shield, 
  Gauge, 
  FileText,
  Menu,
  X,
  ChevronRight,
  Star
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';


function MicroOhmmeters() {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Check if we should show only products (from URL search params)
  const [showOnlyProducts, setShowOnlyProducts] = useState(false);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const productsOnly = searchParams.get('view') === 'products';
    setShowOnlyProducts(productsOnly);

    // If showing only products, set active tab to overview to show product cards
    if (productsOnly) {
      setActiveTab('overview');
    }
  }, [location.search]);

  // Product data
  const products = [
    {
      id: 'ca6240',
      model: 'CA 6240',
      subtitle: '10A Micro-Ohmmeter',
      image: 'https://via.placeholder.com/400x300/FFD700/000000?text=CA+6240+Micro-Ohmmeter',
      current: '10A',
      resistanceRange: '5μΩ to 399.9Ω',
      accuracy: '±0.25% ± 2 counts',
      features: [
        'Large backlit LCD display',
        'Auto measurement mode',
        'Automatic recording mode',
        'Auto power-off function',
        'Memory: 100 measurements',
        'Optical/USB communication',
        'PC interface included',
        'Portable design'
      ],
      specs: [
        'Resistance: 5μΩ to 399.9Ω',
        'Accuracy: ±0.25% ±2 counts',
        'Test current: Up to 10A',
        'Display: Backlit LCD'
      ],
      applications: [
        'Circuit breaker testing',
        'Transformer winding resistance',
        'Motor connection testing'
      ]
    },
    {
      id: 'ca6255',
      model: 'CA 6255',
      subtitle: '10A Advanced Micro-Ohmmeter',
      image: 'https://via.placeholder.com/400x300/FFD700/000000?text=CA+6255+Advanced+Micro-Ohmmeter',
      current: '10A',
      resistanceRange: '5mΩ to 2,500Ω',
      accuracy: '±0.25% ± 2 counts',
      features: [
        'Large backlit LCD display',
        'Auto measurement mode',
        'Automatic discharge system',
        'Auto power-off function',
        'Memory: 1,500 measurements',
        'RS 232 communication',
        'PC interface included',
        'Advanced safety features'
      ],
      specs: [
        'Resistance: 5mΩ to 2,500Ω',
        'Accuracy: ±0.25% ±2 counts',
        'Test current: Up to 10A',
        'Memory: 1,500 measurements'
      ],
      applications: [
        'Power system maintenance',
        'Industrial equipment testing',
        'Quality control applications'
      ]
    },
    {
      id: 'ca6292',
      model: 'CA 6292',
      subtitle: '200A High-Current Micro-Ohmmeter',
      image: 'https://via.placeholder.com/400x300/FFD700/000000?text=CA+6292+High-Current+Micro-Ohmmeter',
      current: '200A',
      resistanceRange: '0.1μΩ to 1Ω',
      accuracy: '±1%',
      features: [
        'Backlit LCD (4 lines x 20 characters)',
        'Internal cooling system',
        'Normal/BSG test modes',
        'Advanced protection systems',
        'Memory: 8,000 measurements',
        'USB communication',
        'PC interface included',
        'High-current capability'
      ],
      specs: [
        'Resistance: 0.1μΩ to 1Ω',
        'Accuracy: ±1%',
        'Test current: Up to 200A',
        'Memory: 8,000 measurements'
      ],
      applications: [
        'Heavy industrial testing',
        'Power plant maintenance',
        'High-current applications'
      ]
    }
  ];

  // Navigation tabs
  const tabs = [
    { id: 'overview', label: 'Overview', icon: Gauge },
    { id: 'comparison', label: 'Compare', icon: Star }
  ];

  // Hero Section Component
  const HeroSection = () => (
    <section className="relative min-h-[60vh] flex items-center justify-center py-6 md:py-12 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
        <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-300 rounded-full opacity-20"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 flex flex-col items-center justify-center w-full">
        <div className="text-center w-full">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8 flex flex-col items-center justify-center w-full"
          >
            <div className="inline-block bg-yellow-400 px-6 py-3 rounded-full mb-4">
              <span className="text-gray-900 font-bold text-lg" style={{ fontFamily: 'Open Sans, sans-serif' }}>
                KRYKARD Precision Instruments
              </span>
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-gray-900 leading-tight mb-4" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              MICRO <span className="text-yellow-400">OHMMETERS</span>
            </h1>

            

            <p className="text-base md:text-lg lg:text-lg text-black leading-relaxed font-medium max-w-4xl mx-auto mb-8" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            Micro-ohm meters are high-precision instruments designed to measure extremely low resistances with exceptional accuracy and stability. They are essential for applications like testing transformer windings, circuit breakers, cables, and welded joints.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center pt-2">
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base"
                onClick={() => navigate('/contact/sales')}
              >
                <span>Request Demo</span>
                <ArrowRight className="h-5 w-5" />
              </button>
              <button
                className="px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:bg-yellow-50 flex items-center justify-center space-x-3 text-base"
                onClick={() => window.open('/public/T&M%20April%202025.pdf', '_blank')}
              >
                <span>View Brochure</span>
                <FileText className="h-5 w-5" />
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );

  // Feature Highlight Component
  const FeatureHighlight = ({ icon: Icon, title, description }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      whileHover={{ y: -8, scale: 1.02 }}
      className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 p-4 md:p-5 h-full border-b-4 border-yellow-400"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      <div className="text-center">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 w-10 h-10 rounded-xl flex items-center justify-center shadow-lg">
            <Icon className="h-6 w-6 text-gray-900" />
          </div>
          <h3 className="text-base md:text-lg font-bold text-gray-900 m-0 p-0">{title}</h3>
        </div>
        <p className="text-gray-700 font-medium text-sm md:text-base leading-relaxed">{description}</p>
      </div>
    </motion.div>
  );

  // Product Overview Card Component
  const ProductOverviewCard = ({ product }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden group h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 p-4">
        <h3 className="text-lg md:text-xl font-bold text-gray-900 text-center">
          {product.model}
        </h3>
      </div>

      {/* Product Image */}
      <div className="p-6">
        <div className="relative h-48 md:h-56 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl overflow-hidden mb-6 flex items-center justify-center">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-20 blur-3xl transform scale-75"></div>
          
          <motion.img
            src={product.image}
            alt={product.model}
            className="h-40 md:h-48 w-auto object-contain drop-shadow-xl relative z-10 mx-auto"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.3 }}
          />
        </div>

        {/* Product Info */}
        <div className="text-center mb-6">
          <div className="inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-bold mb-4">
            {product.current} Test Current
          </div>
          
          <div className="space-y-3">
            {product.features.slice(0, 3).map((feature, index) => (
              <div key={index} className="flex items-center justify-center">
                <Check className="h-4 w-4 text-yellow-500 mr-2 flex-shrink-0" />
                <span className="text-gray-700 font-medium text-sm md:text-base">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* View Details Button */}
        <button
          onClick={() => navigate(`/measure/micro-ohmmeters/product/${product.id}`)}
          className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-6 rounded-xl transition-all duration-300 transform group-hover:-translate-y-1 flex items-center justify-center space-x-2"
        >
          <span>View Details</span>
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>
    </motion.div>
  );

  // Navigation Component
  const Navigation = () => (
    <nav className="sticky top-0 z-50 bg-white shadow-lg border-b border-gray-200" style={{ fontFamily: 'Open Sans, sans-serif' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Desktop Navigation */}
        <div className="hidden md:flex justify-center py-4">
          <div className="bg-gray-100 p-2 rounded-full flex space-x-2">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 font-bold rounded-full transition-all duration-300 flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'bg-yellow-400 text-gray-900 shadow-lg transform -translate-y-0.5'
                    : 'text-gray-600 hover:bg-yellow-50 hover:text-yellow-600'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span className="text-base">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden flex justify-between items-center py-4">
          <span className="font-bold text-gray-900 text-lg">
            {tabs.find(tab => tab.id === activeTab)?.label}
          </span>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-gray-600 hover:text-yellow-600"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-white border-t border-gray-200"
            >
              <div className="py-4 space-y-2">
                {tabs.map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => {
                      setActiveTab(tab.id);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                      activeTab === tab.id
                        ? 'bg-yellow-100 text-yellow-800 font-bold'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <tab.icon className="h-5 w-5" />
                    <span className="text-base">{tab.label}</span>
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </nav>
  );

  // Comparison Table Component
  const ComparisonTable = () => (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden" style={{ fontFamily: 'Open Sans, sans-serif' }}>
      <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 p-6">
        <h3 className="text-xl md:text-2xl font-bold text-center text-gray-900">Model Comparison</h3>
      </div>
      <div className="p-6 overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b-2 border-yellow-200">
              <th className="text-left py-4 px-4 font-bold text-gray-900">Feature</th>
              {products.map(product => (
                <th key={product.id} className="text-center py-4 px-4 font-bold text-gray-900">
                  {product.model}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            <tr className="border-b border-gray-200 hover:bg-yellow-50 transition-colors">
              <td className="py-4 px-4 font-semibold text-gray-900">Resistance Range</td>
              {products.map(product => (
                <td key={product.id} className="py-4 px-4 text-center font-medium text-gray-700">
                  {product.resistanceRange}
                </td>
              ))}
            </tr>
            <tr className="border-b border-gray-200 hover:bg-yellow-50 transition-colors">
              <td className="py-4 px-4 font-semibold text-gray-900">Accuracy</td>
              {products.map(product => (
                <td key={product.id} className="py-4 px-4 text-center font-medium text-gray-700">
                  {product.accuracy}
                </td>
              ))}
            </tr>
            <tr className="border-b border-gray-200 hover:bg-yellow-50 transition-colors">
              <td className="py-4 px-4 font-semibold text-gray-900">Test Current</td>
              {products.map(product => (
                <td key={product.id} className="py-4 px-4 text-center font-medium text-gray-700">
                  Up to {product.current}
                </td>
              ))}
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <PageLayout>
      {/* Show Hero Section only when not in products-only view */}
      {!showOnlyProducts && <HeroSection />}

      {/* Navigation - Hide when showing only products */}
      {!showOnlyProducts && <Navigation />}

      {/* Show only products view */}
      {showOnlyProducts ? (
        <>
          {/* Main Title */}
          <div className="w-full py-6 text-center">
            <h1 className="text-4xl md:text-5xl font-extrabold text-black tracking-tight inline-block border-b-4 border-yellow-400 pb-2">
              Micro-Ohmmeters
            </h1>
          </div>

          {/* Product Cards Section */}
          <section id="products-section" className="py-12 md:py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center mb-10"
              >
                <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                  PROFESSIONAL SERIES
                </div>
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                  Our Micro-Ohmmeter Range
                </h2>
                <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
                  Precision instruments designed for accurate low resistance measurements
                </p>
              </motion.div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {products.map((product) => (
                  <ProductOverviewCard key={product.id} product={product} />
                ))}
              </div>
            </div>
          </section>
        </>
      ) : (
        <>
          {/* Product Overview Section */}
          {activeTab === 'overview' && (
            <section id="products-section" className="py-12 md:py-16">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="text-center mb-10"
                >
                  <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                    PROFESSIONAL SERIES
                  </div>
                  <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                    Our Micro-Ohmmeter Range
                  </h2>
                  <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
                    Precision instruments designed for accurate low resistance measurements
                  </p>
                </motion.div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {products.map((product) => (
                    <ProductOverviewCard key={product.id} product={product} />
                  ))}
                </div>
              </div>
            </section>
          )}
        </>
      )}
      {/* Key Features Section (NEW, modern design) */}
      {activeTab === 'overview' && (
        <section className="py-12 md:py-16">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-10"
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-yellow-500 mb-4">
                Key Features
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto font-medium mb-2">
                Discover the standout features that make our micro-ohmmeters the preferred choice for professionals.
              </p>
            </motion.div>
            <div className="bg-white/80 rounded-2xl shadow-xl p-4 md:p-8 flex flex-col gap-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <FeatureHighlight
                  icon={Zap}
                  title="High Precision"
                  description="Industry-leading measurement accuracy with ranges down to 0.1μΩ for the most demanding applications"
                />
                <FeatureHighlight
                  icon={Shield}
                  title="Robust Design"
                  description="Built for reliability in field and laboratory environments with advanced protection features"
                />
                <FeatureHighlight
                  icon={Gauge}
                  title="Advanced Features"
                  description="Temperature compensation, data storage capabilities, and versatile connectivity options"
                />
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Comparison Section */}
      {activeTab === 'comparison' && (
        <section className="py-12 md:py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-10"
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Compare Our Models
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
                Find the perfect micro-ohmmeter for your specific requirements
              </p>
            </motion.div>
            <ComparisonTable />
          </div>
        </section>
      )}
      {/* Contact Section */}
      <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-yellow-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Need Expert Advice?
            </h2>
            <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
              Our specialists provide comprehensive guidance on electrical measurement solutions
            </p>
            <button
              className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto mt-2"
              onClick={() => navigate('/contact/sales')}
            >
              <span>Contact Sales</span>
              <ArrowRight className="h-5 w-5" />
            </button>
          </motion.div>
        </div>
      </section>
    </PageLayout>
  );
}

export default MicroOhmmeters;